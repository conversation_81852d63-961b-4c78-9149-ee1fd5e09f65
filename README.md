# Employee Management App

A simple application to manage a list of employees.

## Available Scripts

### Starting the Project

To run the app in development mode:

```bash
pnpm dev
```

### Building the Project

To build the app for production:

```bash
pnpm build
```

### Running Tests

#### Unit Tests

To run the unit tests:

```bash
pnpm test
```

#### End-to-End (E2E) Tests

To run the E2E tests in a headless browser:

```bash
pnpm e2e
```

To open the Cypress Test Runner for interactive testing:

```bash
pnpm cypress:open
```
